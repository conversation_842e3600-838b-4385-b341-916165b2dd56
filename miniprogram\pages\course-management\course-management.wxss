/**
 * course-management.wxss - 课程管理页面样式文件
 *
 * 整理说明：
 * 1. 按功能模块组织样式：基础布局 → 顶部区域 → 筛选区域 → 内容区域 → 交互组件
 * 2. 移除重复声明，合并相同样式
 * 3. 统一命名规范和注释格式
 * 4. 保持所有视觉效果不变
 */

/* ==================== 基础布局 ==================== */

/* 页面根元素 - 设置基础高度 */
page {
  height: 100%;
}

/* 页面容器 - 占满可用高度 */
.page {
  height: 100%;
}

/* 主容器 - 页面根容器，定义基础布局 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* ==================== 顶部区域 ==================== */

/* 顶部区域容器 - 固定在顶部，包含主选项卡 */
.top-section {
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 8px;
  background: transparent;
  border: none;
  position: relative;
  z-index: 100;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 选项卡区域容器 - 美化版线条选项卡 */
.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02), 0 0 0 1px rgba(0, 0, 0, 0.01);
  padding: 0px 16px 0 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid #e7e7e7;
  z-index: 10;
}

/* 选项卡容器悬停效果 */
.top-tabs-section:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 82, 217, 0.08);
}

/* 自定义线条选项卡样式 - 覆盖TDesign默认样式 */
.custom-top-tabs {
  background-color: transparent;
  border: none;
  border-radius: 0;
  overflow: visible;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;
  margin: 0;
  width: 100%;
  height: auto;
  min-height: 1px;
}

/* TDesign选项卡导航区域样式 */
.custom-top-tabs .t-tabs__nav {
  padding: 0;
  height: auto;
  min-height: 1px;
  border-bottom: none;
  display: flex;
  align-items: center;
  background: transparent;
}

/* 线条选项卡项目样式 */
.custom-top-tabs .t-tabs__item {
  font-size: 16px !important;
  font-weight: 500;
  padding: 14px 20px !important;
  height: auto;
  line-height: 1.4;
  min-height: 44px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0;
  background: transparent !important;
  border-bottom: 3px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666666 !important;
  position: relative;
}

/* 选项卡项目装饰效果 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 激活状态的线条选项卡 */
.custom-top-tabs .t-tabs__item--active {
  color: #0052d9 !important;
  font-weight: 600 !important;
  border-bottom-color: #0052d9 !important;
  background: transparent !important;
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/* 激活状态的顶部装饰线 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%;
}

/* 非激活状态的选项卡悬停效果 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  color: #333333 !important;
  background: transparent !important;
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/* 悬停时的顶部装饰线 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%;
}

/* 隐藏默认的滑动指示器 */
.custom-top-tabs .t-tabs__track {
  display: none;
}

/* 操作按钮区域 - 包含添加按钮等操作元素 */
.top-actions {
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 顶部操作按钮样式覆盖 - 自定义TDesign按钮外观 */
.top-actions button.t-button {
  background: #52c41a !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  margin-left: 12px !important;
  transition: background 0.2s !important;
  padding: 0 28px !important;
  min-width: unset !important;
  max-width: unset !important;
  width: auto !important;
}

/* 按钮交互状态样式 */
.top-actions button.t-button:active,
.top-actions button.t-button:hover {
  background: #389e0d !important;
  color: #fff !important;
}

/* ==================== 筛选区域 ==================== */

/* 筛选区域 - 固定在顶部，包含子选项卡和搜索功能 */
.filter-section {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  padding: 12px;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: all 0.2s ease;
  overflow: hidden;
  touch-action: none;
  -webkit-overflow-scrolling: auto;
  overscroll-behavior: contain;
  z-index: 90;
  position: relative;
}

/* 筛选区域内元素的触摸行为控制 */
.filter-section view,
.filter-section text,
.filter-section .booking-tabs,
.filter-section .search-actions-section,
.filter-section .collapsed-layout,
.filter-section .expanded-layout,
.filter-section .actions-container {
  touch-action: none;
  -webkit-overflow-scrolling: auto;
}

/* 允许按钮和输入框的正常交互 */
.filter-section .t-button,
.filter-section input,
.filter-section .search-input,
.filter-section .booking-tab,
.filter-section .search-icon-only,
.filter-section .clear-icon,
.filter-section .collapse-icon {
  touch-action: manipulation;
}

/* 自定义子选项卡样式 - 覆盖TDesign默认样式 */
.custom-sub-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  margin: 0;
  max-width: 100%;
}

/* TDesign子选项卡导航区域样式覆盖 */
.custom-sub-tabs .t-tabs__nav {
  padding: 0 8px;
}

/* ==================== 内容区域 ==================== */

/* 课程列表容器 - 包含所有课程卡片 */
.course-list {
  margin-bottom: 0px;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* 时间轴日期分隔 - 为相同日期的课程提供视觉分组 */
.timeline-date {
  margin: 8px;
  font-size: 14px;
  color: #0052d9;
  font-weight: bold;
  text-align: left;
  position: relative;
  padding-left: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 时间轴圆点装饰 - 使用CSS伪元素创建装饰性圆点 */
.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* 课程卡片基础样式 - 现代卡片式设计，支持交互反馈 */
.course-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 课程卡片激活状态 - 提供即时的触觉反馈 */
.course-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/* 新加载卡片的滑入动画 */
.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

/* 滑入动画关键帧定义 - 从下方透明滑入到正常位置 */
@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 课程卡片头部 - 包含课程标题和状态标签 */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

/* 课程标题 - 显示课程名称，支持长文本的优雅处理 */
.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 课程状态标签基础样式 - 显示课程当前状态 */
.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 课程状态标签各种状态样式 */
.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}

.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-status.ended {
  background-color: #f0f0f0;
  color: #888;
}

.course-status.online {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.offline {
  background-color: #fff2e8;
  color: #fa8c16;
}

.course-status.no-status {
  background-color: #f0f0f0;
  color: #999;
}

.course-status.template-status {
  background-color: #e6f3ff;
  color: #1890ff;
}

/* 课程信息列表容器 - 包含课程详细信息 */
.course-info-list {
  margin-bottom: 10px;
}

/* 信息项目 - 单个信息项的容器（时间、教练、场地等） */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 最后一个信息项移除底部边距 */
.info-item:last-child {
  margin-bottom: 0;
}

/* 信息项图标样式 */
.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

/* 信息项文字样式 */
.info-item text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 课程卡片底部区域 - 包含操作按钮 */
.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 操作按钮容器 - 水平排列，支持滚动 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 2px;
  min-width: 0;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 60px;
  max-width: 80px;
}

/* 禁用状态的编辑按钮 */
.edit-disabled-btn {
  background-color: #f0f0f0 !important;
  border-color: #e0e0e0 !important;
  color: #bbb !important;
  cursor: not-allowed;
}

/* 空状态 */


/* 模板列表样式 */
.template-list {
  margin-bottom: 16px;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

.template-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* 压缩间距 */
  gap: 6px; /* 压缩间距 */
}

.template-title {
  font-size: 16px; /* 统一主标题字体大小 */
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-info-list {
  margin-bottom: 16px;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 底部TabBar样式 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e7e7e7;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar {
  background-color: #ffffff;
}

.custom-tab-bar .t-tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item__text {
  font-size: 20rpx;
  margin-top: 4rpx;
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item--active .t-tab-bar-item__text {
  color: #0052d9;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 0 16px; /* 与collapse-header保持一致的左右缩进 */
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
  justify-content: space-between; /* 确保移除按钮靠右对齐 */
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
  margin-right: 8px; /* 为移除图标留出空间 */
}

/* 移除学员图标样式 */
.remove-student-icon {
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-left: auto; /* 确保图标靠右对齐 */
  padding: 4px; /* 增加点击区域 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  border-radius: 4px; /* 圆角效果 */
  transition: background-color 0.2s; /* 平滑的背景色过渡 */
}

/* 移除图标悬停效果 */
.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1); /* 浅红色背景 */
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}

/* 固定卡片宽度，防止内容拉伸 */
.course-card,
.template-card {
  width: 90vw;
  max-width: 700rpx;
  min-width: 320rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.course-content,
.template-content {
  /*
   * 可滚动内容区域样式
   * 占用剩余空间，允许内容滚动
   */
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 禁止水平滚动 */

  width: 100%;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;

  /*
   * 滚动条样式优化（WebKit内核）
   * 在支持的浏览器中提供更美观的滚动条
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 课程状态选项卡样式 - TDesign风格优化 */
.booking-tabs {
 
  display: flex;
  background-color: #f8f9fa; /* 更柔和的背景色 */
  border-radius: 6px; /* 圆角 */
  padding: 8px; /* 内边距 */
  
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  border: 1px solid #e9ecef; /* 添加边框 */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02); /* 内阴影效果 */
}

.booking-tab {
  flex: 1;
  text-align: center;
   /* padding: 6px 8px;调整内边距，更紧凑 */
   padding: 6px;
  font-size: 14px; /* 确保字体不小于14px */
  border-radius: 4px; /* 圆角 */
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
  transition: all 0.2s ease; /* 添加过渡动画 */
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-tab.active {
  background-color: #0052d9;
  color: #ffffff;
  font-weight: 600;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-shadow: 0 1px 3px rgba(0, 82, 217, 0.3); /* 激活状态阴影 */
}

/* 非激活状态的悬停效果 */
.booking-tab:not(.active):hover {
  background-color: #ffffff;
  color: #333333;
}

/* 骨架屏样式 */
.skeleton-card {
  background-color: #f3f3f3;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  min-height: 120px;
  animation: skeleton-fade 1.2s infinite linear;
}
.skeleton {
  background: linear-gradient(90deg, #f3f3f3 25%, #ececec 37%, #f3f3f3 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.2s infinite linear;
  border-radius: 6px;
}
.skeleton-text {
  height: 18px;
  width: 80%;
  margin-bottom: 8px; /* 压缩间距 */
}
.skeleton-block {
  height: 20px;
  width: 60px;
  margin-left: 8px;
}
.skeleton-btn {
  height: 28px;
  width: 60px;
  margin-right: 12px;
  display: inline-block;
}
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
@keyframes skeleton-fade {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 搜索和操作区域样式 - TDesign风格优化 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 36px; /* 减小高度，更紧凑 */

  /* 背景和边框 - 与筛选区域内部元素保持一致 */
  background: #f8f9fa;
  border-radius: 6px;
  padding: 6px; /* 减小内边距 */
  border: 1px solid #e9ecef;
}

/* 收起状态布局 */
.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* 展开状态布局 */
.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1; /* 占据search-actions-section的全部空间 */
}

/* 搜索图标状态 - TDesign风格优化 */
.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px; /* 减小尺寸 */
  height: 32px; /* 减小尺寸 */
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95); /* 添加按压效果 */
}

.search-toggle-icon {
  color: #666666;
  font-size: 14px; /* 统一图标大小 */
}

/* 展开的搜索输入框 - TDesign风格优化 */
.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  padding: 6px 10px; /* 减小内边距 */
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 32px; /* 固定高度，与搜索图标一致 */
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow: 0 0 0 2px rgba(0, 82, 217, 0.1); /* 添加聚焦阴影 */
}

.search-icon {
  color: #999999;
  margin-right: 6px; /* 减小间距 */
  flex-shrink: 0;
  font-size: 14px; /* 调整图标大小 */
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px; /* 确保字体不小于14px */
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 20px; /* 固定输入框高度 */
  line-height: 20px;
}

.search-input::placeholder {
  color: #999999;
  font-size: 14px;
}

.clear-icon {
  color: #999999;
  margin-left: 6px; /* 减小间距 */
  flex-shrink: 0;
  cursor: pointer;
  font-size: 14px;
  padding: 2px; /* 增加点击区域 */
  border-radius: 2px;
  transition: all 0.2s ease;
}

.clear-icon:active {
  color: #666666;
  background: #f5f5f5;
}

.collapse-icon {
  color: #999999;
  margin-left: 6px; /* 减小间距 */
  flex-shrink: 0;
  cursor: pointer;
  font-size: 14px;
  padding: 2px; /* 增加点击区域 */
  border-radius: 2px;
  transition: all 0.2s ease;
}

.collapse-icon:active {
  color: #666666;
  background: #f5f5f5;
}

/* 操作按钮容器 - TDesign风格优化 */
.actions-container {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加间距 */
  flex-shrink: 0;
  width: auto;
  max-width: none;
  justify-content: flex-start;
  animation: buttonsSlideIn 0.3s ease-out;
  overflow: visible;
}

/* 紧凑按钮样式 - TDesign风格，与搜索图标高度一致 */
.actions-container .t-button {
  min-width: auto !important;
  width: auto !important;
  padding: 4px 8px !important; /* 稍微增加水平内边距 */
  white-space: nowrap;
  flex-shrink: 0;
  border-radius: 4px !important;
  height: 32px !important; /* 与搜索图标高度一致 */
  font-size: 14px !important; /* 确保字体不小于14px */
  font-weight: 500 !important;
  transition: all 0.2s ease !important;

  /* 确保垂直居中对齐 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保按钮文字不换行 */
.actions-container .t-button .t-button__content {
  white-space: nowrap;
  min-width: auto !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 进一步压缩按钮内部间距 */
.actions-container .t-button .t-button__text {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
}

/* 移除按钮的最小宽度限制 */
.actions-container .t-button::after {
  display: none !important;
}

/* 强制覆盖TDesign按钮的默认样式 */
.actions-container .t-button.t-button--theme-primary,
.actions-container .t-button.t-button--theme-default,
.actions-container .t-button.t-button--theme-warning {
  min-width: 0 !important;
  width: auto !important;
  padding: 4px 8px !important;
  height: 32px !important; /* 与搜索图标高度一致 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 覆盖按钮内容区域样式 */
.actions-container .t-button .t-button__content {
  min-width: 0 !important;
  width: auto !important;
  display: inline-block !important;
}

/* 搜索展开动画 */
@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 按钮滑入动画 */
@keyframes buttonsSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 批量操作样式 */
.batch-actions-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  margin: 12px 0;
  animation: slideDown 0.3s ease-out;
  box-sizing: border-box;
  width: 100%;
}

.batch-info {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.batch-buttons {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

/* 批量操作按钮样式 - 与添加活动按钮保持一致 */
.batch-buttons .t-button {
  min-width: auto !important;
  width: auto !important;
  padding: 4px 8px !important;
  white-space: nowrap;
  flex-shrink: 0;
  border-radius: 4px !important;
  height: 32px !important; /* 与搜索图标高度一致 */
  font-size: 14px !important;
  font-weight: 500 !important;

  /* 确保垂直居中对齐 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.batch-buttons .t-button .t-button__content {
  white-space: nowrap;
  min-width: auto !important;
  padding: 0 !important;
  margin: 0 !important;
}

.batch-buttons .t-button .t-button__text {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
}

/* 覆盖批量按钮的主题样式 */
.batch-buttons .t-button.t-button--theme-success,
.batch-buttons .t-button.t-button--theme-warning,
.batch-buttons .t-button.t-button--theme-danger {
  min-width: 0 !important;
  width: auto !important;
  padding: 4px 8px !important;
  height: 32px !important; /* 与搜索图标高度一致 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/**
 * 可移动悬浮按钮样式 - 返回今天功能（仅图标）
 *
 * 功能：提供快速跳转到今天活动的可移动悬浮按钮
 * 特性：用户可拖拽移动，灰色半透明设计，仅显示图标
 */
.back-to-today-fab {
  /*
   * 背景和颜色 - 灰色半透明设计
   */
  background: rgba(128, 128, 128, 0.8) !important; /* 灰色半透明背景 */
  color: rgba(255, 255, 255, 0.9) !important; /* 白色半透明图标 */

  /*
   * 尺寸控制 - 圆形按钮，仅图标
   */
  width: 44px !important; /* 固定宽度，圆形设计 */
  height: 44px !important; /* 固定高度，圆形设计 */
  min-width: 44px !important; /* 最小宽度 */
  padding: 0 !important; /* 移除内边距 */

  /*
   * 边框和圆角 - 完全圆形
   */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 22px !important; /* 完全圆形 */

  /*
   * 布局 - 图标居中
   */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /*
   * 阴影效果 - 轻微的灰色阴影
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

  /*
   * 过渡动画 - 平滑的交互效果
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /*
   * 层级控制 - 确保在所有内容之上
   */
  z-index: 999 !important;
}

/*
 * 悬浮按钮图标样式 - 仅图标设计
 */
.back-to-today-fab .t-fab__icon {
  font-size: 16px !important; /* 统一图标大小 */
  color: rgba(255, 255, 255, 0.9) !important;
}

/*
 * 隐藏文字（如果组件自动生成）
 */
.back-to-today-fab .t-fab__text {
  display: none !important;
}

/*
 * 悬浮按钮悬停效果 - 增强可见性
 */
.back-to-today-fab:hover {
  background: rgba(128, 128, 128, 0.9) !important; /* 悬停时稍微不透明 */
  transform: translateY(-1px) !important; /* 轻微上浮 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/*
 * 悬浮按钮按压效果
 */
.back-to-today-fab:active {
  background: rgba(128, 128, 128, 1) !important; /* 按压时完全不透明 */
  transform: translateY(0) scale(0.98) !important; /* 轻微缩放 */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15) !important;
}

/*
 * 拖拽状态样式
 */
.back-to-today-fab.t-fab--dragging {
  background: rgba(128, 128, 128, 1) !important; /* 拖拽时完全不透明 */
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25) !important; /* 增强阴影 */
  transform: scale(1.05) !important; /* 轻微放大 */
}

/* 课程标题行样式 */
.course-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

/* 批量模式下的复选框样式 */
.course-checkbox {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 8px; /* 与文字的间距 */
}

/* 自定义正方形复选框 */
.custom-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 选中状态的复选框 */
.custom-checkbox.checked {
  background: #0052d9;
  border-color: #0052d9;
}

/* 复选框内的对勾图标 */
.check-icon {
  color: #ffffff;
}

/* 批量操作栏滑入动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 触底提示样式 - 参考my-bookings并增强 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 14px; /* 统一正文字体大小 */
  padding: 12px 0 6px 0; /* 压缩内边距 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 14px; /* 统一正文字体大小 */
  padding: 10px 0 6px 0; /* 压缩内边距 */
  letter-spacing: 1px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  position: relative;
  opacity: 0.8;
}

.end-indicator::before,
.end-indicator::after {
  content: '—';
  color: #d0d0d0;
  margin: 0 8px;
  font-size: 12px;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.refresher-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.refresher-icon {
  font-size: 16px; /* 统一图标大小 */
  color: #0052d9;
  font-weight: bold;
  animation: refresher-bounce 1s ease-in-out infinite;
}

.refresher-text {
  font-size: 14px;
  color: #888;
  text-align: center;
}

/* 下拉刷新图标动画 */
@keyframes refresher-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* 加载动画 */
@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
